# Sistema de Multiboxing para AgarIO BR

Este sistema permite controlar múltiplas conexões do agariobr.com.br em uma única aba do navegador, similar ao Sigmally Fixes.

## 🚀 Funcionalidades

-   **Interceptação automática** de conexões WebSocket
-   **Alternância entre conexões** com teclas de atalho
-   **Sincronização de dados** entre múltiplas conexões
-   **Interface visual** mostrando status das conexões
-   **Parsing de mensagens** específico para agar.io
-   **Comandos de controle** (movimento, split, eject, spawn)

## 🎮 Como Usar

### 1. Instalação

-   Carregue o script no agariobr.com.br
-   O sistema será inicializado automaticamente

### 2. Controles

| Tecla        | Função                         |
| ------------ | ------------------------------ |
| `Ctrl + Tab` | Alternar entre conexões        |
| `F2`         | Criar conexão secundária       |
| `F3`         | Ativar/desativar sincronização |

### 3. Interface Visual

No canto superior direito aparecerá um painel mostrando:

-   Status de cada conexão (🟢 conectado / 🔴 desconectado)
-   Conexão ativa (👉)
-   Número de células próprias e visíveis
-   Ping de cada conexão
-   Estado da sincronização

## 🔧 Como Funciona

### Interceptação de WebSocket

```typescript
// O sistema intercepta todas as conexões WebSocket
window.WebSocket = class extends WebSocket {
	constructor(url: string | URL, protocols?: string | string[]) {
		super(url, protocols);
		// Intercepta conexões do agariobr.com.br
		if (urlString.includes("agariobr.com.br")) {
			self.handleInterceptedSocket(this, urlString);
		}
	}
};
```

### Sistema de Views

Cada conexão tem sua própria "view" com:

-   Câmera (posição x, y, zoom)
-   Células próprias
-   Células visíveis
-   Leaderboard
-   Estatísticas

### Sincronização

Quando ativada, o sistema:

-   Combina dados de múltiplas conexões
-   Calcula posições médias ponderadas por timestamp
-   Cria uma visão unificada das células

## 📡 Protocolo de Mensagens

O sistema reconhece os seguintes opcodes:

| Opcode | Descrição                  |
| ------ | -------------------------- |
| `0x10` | Atualização de células     |
| `0x11` | Atualização de posição     |
| `0x12` | Atualização do leaderboard |
| `0x14` | Limpar células             |
| `0x20` | Remover células            |
| `0x30` | Definir bordas do mapa     |
| `0x40` | Definir células próprias   |

## 🛠️ Personalização

### Adicionar Novos Opcodes

```typescript
case 0x50: // Novo opcode
    this.handleCustomMessage(connectionId, dataView);
    break;
```

### Modificar Sincronização

```typescript
// Alterar peso baseado na idade dos dados
const weight = Math.max(0.1, 1 / (age + 100));
```

### Customizar Interface

```typescript
// Modificar CSS do painel de status
statusDiv.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    // ... seus estilos
`;
```

## 🐛 Debugging

### Console Commands

```javascript
// Acessar o sistema
const gameWorld = GameWorld.getInstance();

// Ver conexões ativas
console.log(gameWorld.getAllConnections());

// Ver células
console.log(gameWorld.cells);

// Alternar conexão manualmente
gameWorld.switchConnection("secondary_1");

// Enviar comando personalizado
gameWorld.sendToActiveConnection(customData);
```

### Logs Importantes

-   `WebSocket interceptado:` - Nova conexão detectada
-   `Conexão X estabelecida` - Handshake completado
-   `Alternando de X para Y` - Mudança de conexão ativa
-   `Erro ao processar mensagem` - Problema no parsing

## ⚠️ Limitações

1. **Detecção de protocolo**: Os opcodes podem variar entre versões do agariobr.com.br
2. **Rate limiting**: Servidores podem limitar múltiplas conexões do mesmo IP
3. **Sincronização**: Pode haver lag entre conexões com pings diferentes
4. **Parsing**: Estrutura das mensagens pode mudar com atualizações

## 🔄 Próximos Passos

Para melhorar o sistema:

1. **Análise de tráfego**: Capture mensagens reais do agariobr.com.br para ajustar opcodes
2. **Auto-detecção**: Implementar detecção automática do formato de mensagens
3. **Proxy support**: Adicionar suporte a proxies para múltiplos IPs
4. **Renderização**: Integrar com o sistema de renderização do jogo
5. **Configurações**: Adicionar painel de configurações avançadas

## 📝 Notas Técnicas

-   O sistema usa `Symbol()` para identificadores únicos de conexões
-   Mensagens são parseadas usando `DataView` para precisão de bytes
-   Sincronização usa média ponderada baseada em timestamps
-   Interface é criada dinamicamente com CSS inline
-   Keybinds são registrados no sistema existente do script
