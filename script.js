"use strict";
// ==UserScript==
// @name         Script Teste
// @namespace    script-test
// @version      1.0.0
// <AUTHOR>
// @description  Um script de teste para demonstrar funcionalidades avançadas do Tampermonkey
// @license MIT
// @match        https://agariobr.com.br/*
// @grant        none
// @run-at       document-idle
// ==/UserScript==
// ===== CONFIGURAÇÕES =====
const CONFIG = {
    SCRIPT_NAME: "teste",
    VERSION: "1.0.30",
    STORAGE_KEYS: {
        SETTINGS: "settings",
    },
};
// ===== SERVIÇOS DE ARMAZENAMENTO ESSENCIAL =====
class StorageService {
    static get(key) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : null;
        }
        catch (_a) {
            return null;
        }
    }
    static setJSON(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
            return true;
        }
        catch (_a) {
            return false;
        }
    }
    static remove(key) {
        try {
            localStorage.removeItem(key);
            return true;
        }
        catch (_a) {
            return false;
        }
    }
}
class DOMUtilities {
    static createElement(tag, options = {}) {
        const element = document.createElement(tag);
        if (options.className)
            element.className = options.className;
        if (options.textContent)
            element.textContent = options.textContent;
        if (options.styles)
            Object.assign(element.style, options.styles);
        if (options.attributes)
            Object.entries(options.attributes).forEach(([key, value]) => {
                element.setAttribute(key, value);
            });
        if (options.eventListeners)
            Object.entries(options.eventListeners).forEach(([event, listener]) => {
                element.addEventListener(event, listener);
            });
        return element;
    }
    static removeElement(element) {
        if (element && element.parentNode) {
            element.parentNode.removeChild(element);
            return true;
        }
        return false;
    }
}
class SettingsStore {
    constructor() {
        this.settings = {};
        this.storageKey = CONFIG.STORAGE_KEYS.SETTINGS;
        this.loadSettings();
    }
    static getInstance() {
        if (!SettingsStore.instance) {
            SettingsStore.instance = new SettingsStore();
        }
        return SettingsStore.instance;
    }
    loadSettings() {
        const savedSettings = StorageService.get(this.storageKey);
        if (savedSettings && typeof savedSettings === "object") {
            this.settings = savedSettings;
        }
    }
    saveSettings() {
        return StorageService.setJSON(this.storageKey, this.settings);
    }
    getAllSettings() {
        return Object.assign({}, this.settings);
    }
    getSetting(key) {
        return this.settings[key] || null;
    }
    setSetting(key, value) {
        this.settings[key] = value;
        return this.saveSettings();
    }
}
class KeyBindManager {
    constructor() {
        this.keyBindings = new Map();
        this.setupGlobalListener();
    }
    static getInstance() {
        if (!KeyBindManager.instance) {
            KeyBindManager.instance = new KeyBindManager();
        }
        return KeyBindManager.instance;
    }
    setupGlobalListener() {
        document.addEventListener("keydown", event => {
            const key = event.key.toLowerCase();
            const binding = this.keyBindings.get(key);
            if (binding) {
                const result = binding.handler(event);
                if (result !== false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
            }
        });
    }
    register(binding) {
        const key = binding.key.toLowerCase();
        this.keyBindings.set(key, binding);
        return true;
    }
    listBindings() {
        return Array.from(this.keyBindings.values());
    }
}
// ===== INTERCEPTADOR DE WEBSOCKET =====
class WebSocketInterceptor {
    constructor() {
        this.targetUrl = "wss://servers.agariobr.com.br:4409";
        this.interceptedSocket = null;
        this.logInterval = null;
        this.messageBuffer = [];
        this.originalWebSocket = window.WebSocket;
        this.setupWebSocketInterception();
    }
    static getInstance() {
        if (!WebSocketInterceptor.instance) {
            WebSocketInterceptor.instance = new WebSocketInterceptor();
        }
        return WebSocketInterceptor.instance;
    }
    setupWebSocketInterception() {
        const self = this;
        window.WebSocket = class extends self.originalWebSocket {
            constructor(url, protocols) {
                super(url, protocols);
                const urlString = url.toString();
                if (urlString.includes("servers.agariobr.com.br:4409")) {
                    console.log(`🔗 WebSocket interceptado: ${urlString}`);
                    self.interceptedSocket = this;
                    self.startLogging();
                    self.setupSocketEventListeners(this);
                }
            }
        };
        // Preservar propriedades do WebSocket original
        Object.setPrototypeOf(window.WebSocket.prototype, this.originalWebSocket.prototype);
        Object.setPrototypeOf(window.WebSocket, this.originalWebSocket);
    }
    setupSocketEventListeners(socket) {
        const originalSend = socket.send.bind(socket);
        // Interceptar mensagens enviadas
        socket.send = (data) => {
            this.messageBuffer.push({
                timestamp: Date.now(),
                data: data,
                type: "sent",
            });
            return originalSend(data);
        };
        // Interceptar mensagens recebidas
        const originalOnMessage = socket.onmessage;
        socket.addEventListener("message", event => {
            this.messageBuffer.push({
                timestamp: Date.now(),
                data: event.data,
                type: "received",
            });
        });
        // Eventos de conexão
        socket.addEventListener("open", () => {
            console.log("🟢 WebSocket conectado ao servidor");
        });
        socket.addEventListener("close", () => {
            console.log("🔴 WebSocket desconectado do servidor");
            this.stopLogging();
            this.interceptedSocket = null;
        });
        socket.addEventListener("error", error => {
            console.log("❌ Erro no WebSocket:", error);
        });
    }
    startLogging() {
        if (this.logInterval) {
            clearInterval(this.logInterval);
        }
        this.logInterval = setInterval(() => {
            if (this.interceptedSocket && this.interceptedSocket.readyState === WebSocket.OPEN) {
                this.logSocketStatus();
                this.logRecentMessages();
            }
        }, 3000);
        console.log("📊 Iniciado logging a cada 3 segundos");
    }
    stopLogging() {
        if (this.logInterval) {
            clearInterval(this.logInterval);
            this.logInterval = null;
            console.log("⏹️ Parou logging do WebSocket");
        }
    }
    logSocketStatus() {
        if (!this.interceptedSocket)
            return;
        const states = {
            [WebSocket.CONNECTING]: "CONNECTING",
            [WebSocket.OPEN]: "OPEN",
            [WebSocket.CLOSING]: "CLOSING",
            [WebSocket.CLOSED]: "CLOSED",
        };
        console.group("📡 Status WebSocket (servers.agariobr.com.br:4409)");
        console.log(`Estado: ${states[this.interceptedSocket.readyState]}`);
        console.log(`URL: ${this.interceptedSocket.url}`);
        console.log(`Protocolo: ${this.interceptedSocket.protocol || "N/A"}`);
        console.log(`Extensões: ${this.interceptedSocket.extensions || "N/A"}`);
        console.log(`Mensagens no buffer: ${this.messageBuffer.length}`);
        console.groupEnd();
    }
    logRecentMessages() {
        const now = Date.now();
        const recentMessages = this.messageBuffer.filter(msg => now - msg.timestamp < 3000);
        if (recentMessages.length > 0) {
            console.group("📨 Mensagens dos últimos 3 segundos");
            recentMessages.forEach((msg, index) => {
                const timeStr = new Date(msg.timestamp).toLocaleTimeString();
                const icon = msg.type === "sent" ? "📤" : "📥";
                console.log(`${icon} [${timeStr}] ${msg.type.toUpperCase()}:`, msg.data);
            });
            console.groupEnd();
        }
        // Limpar mensagens antigas (manter apenas últimos 100)
        if (this.messageBuffer.length > 100) {
            this.messageBuffer = this.messageBuffer.slice(-100);
        }
    }
    // Método público para obter informações do WebSocket
    getSocketInfo() {
        if (!this.interceptedSocket)
            return null;
        return {
            url: this.interceptedSocket.url,
            readyState: this.interceptedSocket.readyState,
            protocol: this.interceptedSocket.protocol,
            extensions: this.interceptedSocket.extensions,
            messagesCount: this.messageBuffer.length,
        };
    }
    // Método público para obter mensagens recentes
    getRecentMessages(seconds = 10) {
        const cutoff = Date.now() - seconds * 1000;
        return this.messageBuffer.filter(msg => msg.timestamp > cutoff);
    }
}
class UIManager {
    constructor() {
        this.isInitialized = false;
        this.keyBindManager = KeyBindManager.getInstance();
    }
    static getInstance() {
        if (!UIManager.instance)
            UIManager.instance = new UIManager();
        return UIManager.instance;
    }
    initialize() {
        if (this.isInitialized)
            return true;
        this.keyBindManager.register({
            key: "F1",
            handler: () => alert("Ajuda"),
            description: "Mostra a ajuda",
        });
        // Keybind para mostrar status do WebSocket
        this.keyBindManager.register({
            key: "F2",
            handler: () => {
                const interceptor = WebSocketInterceptor.getInstance();
                const info = interceptor.getSocketInfo();
                console.log("🔍 Informações do WebSocket:", info);
                const recent = interceptor.getRecentMessages(30);
                console.log("🕒 Mensagens dos últimos 30 segundos:", recent);
            },
            description: "Mostra informações do WebSocket",
        });
        this.isInitialized = true;
        return true;
    }
    destroy() {
        this.isInitialized = false;
    }
}
// ===== APLICAÇÃO PRINCIPAL ESSENCIAL =====
class ScriptApplication {
    constructor() {
        this.isInitialized = false;
        this.settingsStore = SettingsStore.getInstance();
        this.uiManager = UIManager.getInstance();
        this.wsInterceptor = WebSocketInterceptor.getInstance();
    }
    static getInstance() {
        if (!ScriptApplication.instance)
            ScriptApplication.instance = new ScriptApplication();
        return ScriptApplication.instance;
    }
    async initialize() {
        if (this.isInitialized)
            return true;
        if (document.readyState === "loading") {
            await new Promise(resolve => document.addEventListener("DOMContentLoaded", resolve));
        }
        this.uiManager.initialize();
        this.settingsStore.setSetting("appVersion", CONFIG.VERSION);
        console.log("🚀 Script inicializado com interceptação WebSocket");
        console.log("ℹ️  Pressione F2 para ver status do WebSocket");
        this.isInitialized = true;
        return true;
    }
}
// ===== INICIALIZAÇÃO AUTOMÁTICA =====
ScriptApplication.getInstance().initialize();
